package de.sarocesch.sarosinteractiveblocks;

import com.mojang.brigadier.Command;
import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.SharedSuggestionProvider;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.storage.LevelResource;
import net.minecraft.world.phys.BlockHitResult;
import net.minecraft.world.phys.HitResult;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.eventbus.api.listener.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mod.EventBusSubscriber
public class BlockActionCommand {

    private static final Map<BlockPos, File> blockActionCache = new HashMap<>();

    @SubscribeEvent
    public static void onCommandRegister(RegisterCommandsEvent event) {
        register(event.getDispatcher());
    }

    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(Commands.literal("blockaction")
                .then(Commands.literal("create")
                        .then(Commands.argument("side", StringArgumentType.word())
                                .suggests((context, builder) -> SharedSuggestionProvider.suggest(new String[]{"S", "C"}, builder))
                                .then(Commands.argument("action", StringArgumentType.word())
                                        .suggests((context, builder) -> SharedSuggestionProvider.suggest(new String[]{"R", "L", "T", "S"}, builder))
                                        .then(Commands.argument("command", StringArgumentType.greedyString())
                                                .executes(context -> {
                                                    try {
                                                        return createBlockAction(context);
                                                    } catch (IOException | CommandSyntaxException e) {
                                                        context.getSource().sendFailure(Component.literal("Failed to create block action: " + e.getMessage()));
                                                        return Command.SINGLE_SUCCESS;
                                                    }
                                                })
                                        )
                                )
                        )
                )
                .then(Commands.literal("delete")
                        .executes(context -> {
                            try {
                                return deleteBlockAction(context);
                            } catch (IOException | CommandSyntaxException e) {
                                context.getSource().sendFailure(Component.literal("Failed to delete block action: " + e.getMessage()));
                                return Command.SINGLE_SUCCESS;
                            }
                        }))
                .then(Commands.literal("info")
                        .executes(context -> {
                            try {
                                return infoBlockAction(context);
                            } catch (IOException | CommandSyntaxException e) {
                                context.getSource().sendFailure(Component.literal("Failed to retrieve block action info: " + e.getMessage()));
                                return Command.SINGLE_SUCCESS;
                            }
                        }))
        );
    }

    private static BlockPos getPlayerLookAtPos(ServerPlayer player) {
        HitResult rayTraceResult = player.pick(20.0D, 0.0F, false);
        if (rayTraceResult.getType() == HitResult.Type.BLOCK) {
            BlockHitResult blockHitResult = (BlockHitResult) rayTraceResult;
            return blockHitResult.getBlockPos();
        }
        return null;
    }

    private static int createBlockAction(CommandContext<CommandSourceStack> context) throws IOException, CommandSyntaxException {
        ServerPlayer player = context.getSource().getPlayerOrException();
        BlockPos pos = getPlayerLookAtPos(player);

        if (pos == null) {
            context.getSource().sendFailure(Component.literal("You are not looking at a block!"));
            return Command.SINGLE_SUCCESS;
        }

        ServerLevel world = (ServerLevel) player.level();
        String side = StringArgumentType.getString(context, "side");
        String action = StringArgumentType.getString(context, "action");
        String command = StringArgumentType.getString(context, "command");

        File worldFolder = world.getServer().getWorldPath(LevelResource.ROOT).toFile();
        File blockActionFolder = new File(worldFolder, "BlockActions");

        if (!blockActionFolder.exists()) {
            blockActionFolder.mkdirs();
        }

        File blockFile = new File(blockActionFolder, pos.getX() + "_" + pos.getY() + "_" + pos.getZ() + ".txt");

        List<String> lines;
        if (blockFile.exists()) {
            lines = Files.readAllLines(blockFile.toPath(), StandardCharsets.UTF_8);
        } else {
            lines = new java.util.ArrayList<>();
        }

        lines.add("Side: " + side + ", Action: " + action + ", Command: " + command);
        Files.write(blockFile.toPath(), lines);

        player.sendSystemMessage(Component.literal("Block action created at X: " + pos.getX() + ", Y: " + pos.getY() + ", Z: " + pos.getZ() + ", Side: " + side + ", " + action + " with command: " + command));

        blockActionCache.put(pos, blockFile);

        return Command.SINGLE_SUCCESS;
    }

    private static int deleteBlockAction(CommandContext<CommandSourceStack> context) throws IOException, CommandSyntaxException {
        ServerPlayer player = context.getSource().getPlayerOrException();
        BlockPos pos = getPlayerLookAtPos(player);

        if (pos == null) {
            context.getSource().sendFailure(Component.literal("You are not looking at a block!"));
            return Command.SINGLE_SUCCESS;
        }

        ServerLevel world = (ServerLevel) player.level();
        File worldFolder = world.getServer().getWorldPath(LevelResource.ROOT).toFile();
        File blockActionFolder = new File(worldFolder, "BlockActions");
        File blockFile = new File(blockActionFolder, pos.getX() + "_" + pos.getY() + "_" + pos.getZ() + ".txt");

        if (!blockFile.exists()) {
            context.getSource().sendFailure(Component.literal("BlockAction does not exist at " + pos.getX() + ", Y: " + pos.getY() + ", Z: " + pos.getZ() + ", "));
            return Command.SINGLE_SUCCESS;
        }

        Files.delete(blockFile.toPath());
        blockActionCache.remove(pos);
        context.getSource().sendSuccess(() -> Component.literal("BlockAction deleted at " + pos.getX() + ", Y: " + pos.getY() + ", Z: " + pos.getZ() + ", "), true);

        return Command.SINGLE_SUCCESS;
    }

    private static int infoBlockAction(CommandContext<CommandSourceStack> context) throws IOException, CommandSyntaxException {
        ServerPlayer player = context.getSource().getPlayerOrException();
        BlockPos pos = getPlayerLookAtPos(player);

        if (pos == null) {
            context.getSource().sendFailure(Component.literal("You are not looking at a block!"));
            return Command.SINGLE_SUCCESS;
        }

        ServerLevel world = context.getSource().getLevel();
        File worldFolder = world.getServer().getWorldPath(LevelResource.ROOT).toFile();
        File blockActionFolder = new File(worldFolder, "BlockActions");
        File blockFile = new File(blockActionFolder, pos.getX() + "_" + pos.getY() + "_" + pos.getZ() + ".txt");

        if (blockFile.exists()) {
            List<String> lines = Files.readAllLines(blockFile.toPath());
            for (String line : lines) {
                String formattedLine = line.replace("Side: S", "Side: Server")
                        .replace("Side: C", "Side: Client")
                        .replace("R", "Right-click")
                        .replace("L", "Left-click")
                        .replace("S", "Redstone")
                        .replace("T", "Touch");

                context.getSource().sendSuccess(() -> Component.literal(formattedLine), true);
            }
        } else {
            context.getSource().sendFailure(Component.literal("BlockAction does not exist at " + pos.getX() + ", Y: " + pos.getY() + ", Z: " + pos.getZ() + ", "));
        }

        return Command.SINGLE_SUCCESS;
    }
}
