package de.sarocesch.sarosinteractiveblocks;

import net.minecraftforge.event.server.ServerStartedEvent;
import net.minecraftforge.eventbus.api.bus.BusGroup;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.lang.invoke.MethodHandles;

@Mod(SarosInteractiveBlocks.MODID)
public class SarosInteractiveBlocks {
    public static final Logger LOGGER = LogManager.getLogger(SarosInteractiveBlocks.class);
    public static final String MODID = "sarosinteractiveblocks";

    protected final FMLJavaModLoadingContext context;

    public SarosInteractiveBlocks(FMLJavaModLoadingContext context) {
        this.context = context;

        // Register content to the mod event bus if needed
        var modBusGroup = context.getModBusGroup();

        // Common setup event listener
        FMLCommonSetupEvent.getBus(modBusGroup).addListener(this::commonSetup);

        // Forge Event Listeners
        ServerStartedEvent.BUS.addListener(this::onServerStart);

        // Listener classes
        BusGroup.DEFAULT.register(MethodHandles.lookup(), new BlockActionCommand());
        BusGroup.DEFAULT.register(MethodHandles.lookup(), new BlockActionListener());
    }

    private void commonSetup(final FMLCommonSetupEvent event) {
        LOGGER.info("Saros Interactive Blocks - common setup initialized.");
    }

    private void onServerStart(final ServerStartedEvent event) {
        LOGGER.info("Saros Interactive Blocks has started successfully.");
    }
}
