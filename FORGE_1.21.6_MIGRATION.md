# Minecraft Forge 1.21.6 Migration Guide: EventBus 7 Changes

## Overview

Minecraft Forge 1.21.6 introduces significant changes to the EventBus system with the new EventBus 7, which implements strict compile-time validation to improve performance in production environments. This guide documents the breaking changes and provides solutions for migrating existing mods.

## EventBus 7 Changes

### New Strict Validation System

EventBus 7 shifts most runtime validation to compile-time via an annotation processor. This change:
- Improves performance in production by reducing runtime checks
- Provides earlier error detection during development
- Enforces stricter rules for event handler registration
- Requires proper annotation usage on all event-related classes

### Annotation Processor

The new annotation processor is automatically included in Forge 1.21.6+:

```gradle
// This is automatically included in build.gradle for Forge 1.21.6+
annotationProcessor 'net.minecraftforge:eventbus-validator:7.0-beta.9'
```

## Import Path Updates

### Correct Import for @SubscribeEvent

**✅ Correct Import (Forge 1.21.6+):**
```java
import net.minecraftforge.eventbus.api.listener.SubscribeEvent;
```

**❌ Incorrect Import (causes compilation errors):**
```java
import net.minecraftforge.eventbus.api.SubscribeEvent; // This path doesn't exist in 1.21.6
```

## Common Errors and Solutions

### 1. "Missing @SubscribeEvent annotation" on Private Helper Methods

**❌ Problematic Code:**
```java
@Mod.EventBusSubscriber(modid = MyMod.MODID)
public class BlockInteractionHandler {
    
    @SubscribeEvent
    public static void onRightClickBlock(PlayerInteractEvent.RightClickBlock event) {
        if (state.getBlock() instanceof MyBlock) {
            handleMyBlockInteraction(event, state, level, pos, player, hand);
        }
    }
    
    // ERROR: EventBus 7 tries to register this as an event handler
    private static void handleMyBlockInteraction(PlayerInteractEvent.RightClickBlock event, 
                                               BlockState state, Level level, BlockPos pos, 
                                               Player player, InteractionHand hand) {
        // Helper method logic
    }
}
```

**✅ Fixed Code:**
```java
@Mod.EventBusSubscriber(modid = MyMod.MODID)
public class BlockInteractionHandler {
    
    @SubscribeEvent
    public static void onRightClickBlock(PlayerInteractEvent.RightClickBlock event) {
        Player player = event.getEntity();
        Level level = player.level();
        BlockPos pos = event.getPos();
        BlockState state = level.getBlockState(pos);
        InteractionHand hand = event.getHand();
        
        if (state.getBlock() instanceof MyBlock) {
            // Inline the helper method logic directly
            MyBlock block = (MyBlock) state.getBlock();
            Vec3 hitVec = new Vec3(pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5);
            BlockHitResult hit = new BlockHitResult(hitVec, event.getFace(), pos, false);
            InteractionResult result = block.use(state, level, pos, player, hand, hit);
        }
    }
}
```

### 2. "No declared methods found" on Registry Classes

**❌ Problematic Code:**
```java
@Mod.EventBusSubscriber  // ERROR: This class has no @SubscribeEvent methods
public class MyModFeatures {
    public static final DeferredRegister<Feature<?>> REGISTRY = 
        DeferredRegister.create(ForgeRegistries.FEATURES, MyMod.MODID);
    
    public static final RegistryObject<Feature<?>> MY_FEATURE = 
        REGISTRY.register("my_feature", MyFeature::new);
}
```

**✅ Fixed Code:**
```java
// Remove @Mod.EventBusSubscriber - this is just a registry class
public class MyModFeatures {
    public static final DeferredRegister<Feature<?>> REGISTRY = 
        DeferredRegister.create(ForgeRegistries.FEATURES, MyMod.MODID);
    
    public static final RegistryObject<Feature<?>> MY_FEATURE = 
        REGISTRY.register("my_feature", MyFeature::new);
}
```

### 3. "No listeners found" on Network Message Classes

**❌ Problematic Code:**
```java
@Mod.EventBusSubscriber(bus = Mod.EventBusSubscriber.Bus.MOD)  // ERROR: Not an event handler
public class MyNetworkMessage {
    private final int data;
    
    public MyNetworkMessage(int data) {
        this.data = data;
    }
    
    public static void handle(MyNetworkMessage message, ServerPlayer player) {
        // Message handling logic
    }
}
```

**✅ Fixed Code:**
```java
// Remove @Mod.EventBusSubscriber - this is a network message, not an event handler
public class MyNetworkMessage {
    private final int data;
    
    public MyNetworkMessage(int data) {
        this.data = data;
    }
    
    public static void handle(MyNetworkMessage message, ServerPlayer player) {
        // Message handling logic
    }
}
```

## Registry Changes and Double Registration

### Double Registration Issue

**❌ Problematic Code:**
```java
public MyMod(FMLJavaModLoadingContext context) {
    // First registration
    MyModMenus.REGISTRY.register(context.getModBusGroup());
    
    var modBusGroup = context.getModBusGroup();
    
    // Second registration - causes override warnings
    MyModMenus.REGISTRY.register(modBusGroup);
}
```

**✅ Fixed Code:**
```java
public MyMod(FMLJavaModLoadingContext context) {
    var modBusGroup = context.getModBusGroup();
    
    // Single registration
    MyModBlocks.register(modBusGroup);
    MyModItems.register(modBusGroup);
    MyModMenus.REGISTRY.register(modBusGroup);
    MyModFeatures.REGISTRY.register(modBusGroup);
}
```

## Best Practices for Forge 1.21.6

### 1. Event Handler Classes
- Only use `@Mod.EventBusSubscriber` on classes that contain `@SubscribeEvent` methods
- Avoid private helper methods in event handler classes
- Inline helper logic directly into event handler methods
- Use proper import: `net.minecraftforge.eventbus.api.listener.SubscribeEvent`

### 2. Registry Classes
- Do NOT use `@Mod.EventBusSubscriber` on registry classes
- Registry classes should only contain `DeferredRegister` and `RegistryObject` declarations
- Register registries only once in your mod constructor

### 3. Network Message Classes
- Do NOT use `@Mod.EventBusSubscriber` on network message classes
- Network messages are handled through the network system, not the event bus

### 4. Utility Classes
- Move helper methods to separate utility classes without event annotations
- Keep event handlers focused and minimal

## Migration Steps

### Step 1: Update Imports
Replace all EventBus-related imports:
```java
// Old
import net.minecraftforge.eventbus.api.SubscribeEvent;

// New
import net.minecraftforge.eventbus.api.listener.SubscribeEvent;
```

### Step 2: Review @Mod.EventBusSubscriber Usage
1. Find all classes with `@Mod.EventBusSubscriber`
2. Verify each class contains at least one `@SubscribeEvent` method
3. Remove the annotation from registry, network, and utility classes

### Step 3: Refactor Event Handlers
1. Identify private helper methods in event handler classes
2. Inline the logic directly into the event handler methods
3. Move complex logic to separate utility classes if needed

### Step 4: Fix Registry Registration
1. Check for duplicate registry registrations in mod constructor
2. Ensure each registry is registered only once
3. Remove `@Mod.EventBusSubscriber` from registry classes

### Step 5: Test and Validate
1. Run `./gradlew build` to check for compilation errors
2. Run `./gradlew runClient` to test mod loading
3. Verify all event handlers work correctly

## Troubleshooting

### Common Error Messages

| Error Message | Cause | Solution |
|---------------|-------|----------|
| `Missing @SubscribeEvent annotation` | Private method in event handler class | Inline method or move to utility class |
| `No declared methods found` | `@Mod.EventBusSubscriber` on non-event class | Remove the annotation |
| `No listeners found` | `@Mod.EventBusSubscriber` on wrong class type | Remove the annotation |
| `Override did not have an associated owner object` | Double registry registration | Remove duplicate registration |

### Debug Tips
1. Enable debug logging: `property 'forge.logging.console.level', 'debug'`
2. Check the annotation processor output for validation errors
3. Use the EventBus validator to catch issues at compile time

## Conclusion

The EventBus 7 changes in Forge 1.21.6 require careful attention to annotation usage and event handler structure. While the migration requires some refactoring, the new system provides better performance and earlier error detection. Follow this guide to ensure your mod works correctly with the new EventBus system.

For more information, refer to the official Forge documentation and the EventBus 7 changelog.
