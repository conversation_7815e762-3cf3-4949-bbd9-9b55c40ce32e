package de.sarocesch.sarosinteractiveblocks;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.ParseResults;
import com.mojang.brigadier.StringReader;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.core.BlockPos;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.entity.player.Player;
import net.minecraft.network.chat.Component;
import net.minecraft.ChatFormatting;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.event.entity.player.PlayerInteractEvent;
import net.minecraftforge.event.level.BlockEvent;
import net.minecraftforge.eventbus.api.listener.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraft.world.level.storage.LevelResource;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.List;

@Mod.EventBusSubscriber
public class BlockActionListener {

    @SubscribeEvent
    public static void onBlockLeftClick(PlayerInteractEvent.LeftClickBlock event) {
        if (!event.getLevel().isClientSide()) {
            ServerPlayer player = (ServerPlayer) event.getEntity();
            BlockPos pos = event.getPos();
            ServerLevel world = (ServerLevel) event.getLevel();
            Commands commands = world.getServer().getCommands();

            File blockFile = new File(world.getServer().getWorldPath(LevelResource.ROOT).toFile(), "BlockActions/" + pos.getX() + "_" + pos.getY() + "_" + pos.getZ() + ".txt");

            if (blockFile.exists()) {
                try {
                    List<String> lines = Files.readAllLines(blockFile.toPath(), StandardCharsets.UTF_8);
                    for (String line : lines) {
                        if (line.contains("Action: L") && !player.isShiftKeyDown()) {
                            String command = line.substring(line.indexOf("Command: ") + 9).trim();
                            if (command.startsWith("/")) {
                                command = command.substring(1);
                                command = command.replace("<player>", player.getName().getString());
                            }

                            ParseResults<CommandSourceStack> parseResults = commands.getDispatcher().parse(new StringReader(command), player.createCommandSourceStack());
                            if (line.contains("Side: C")) {
                                commands.performCommand(parseResults, command);  // Übergabe des Befehls-Strings
                            } else if (line.contains("Side: S")) {
                                ParseResults<CommandSourceStack> serverParseResults = commands.getDispatcher().parse(new StringReader(command), world.getServer().createCommandSourceStack());
                                commands.performCommand(serverParseResults, command);  // Übergabe des Befehls-Strings
                            }

                            event.setCancellationResult(InteractionResult.FAIL);
                            return;
                        }
                    }
                } catch (Exception e) {
                    return;
                }
            }
        }
    }

    @SubscribeEvent
    public static void onBlockBreak(net.minecraftforge.event.level.BlockEvent.BreakEvent event) {
        if (!event.getLevel().isClientSide()) {
            ServerPlayer player = (ServerPlayer) event.getPlayer();
            BlockPos pos = event.getPos();
            ServerLevel world = (ServerLevel) event.getLevel();

            File blockFile = new File(world.getServer().getWorldPath(LevelResource.ROOT).toFile(), "BlockActions/" + pos.getX() + "_" + pos.getY() + "_" + pos.getZ() + ".txt");

            if (blockFile.exists() && blockFile.delete()) {
                player.sendSystemMessage(Component.literal("File successfully deleted.").withStyle(ChatFormatting.RED));
            }
        }
    }

    @SubscribeEvent
    public static void onBlockRightClick(PlayerInteractEvent.RightClickBlock event) {
        if (!event.getLevel().isClientSide()) {
            ServerPlayer player = (ServerPlayer) event.getEntity();
            BlockPos pos = event.getPos();
            ServerLevel world = (ServerLevel) event.getLevel();
            Commands commands = world.getServer().getCommands();

            File blockFile = new File(world.getServer().getWorldPath(LevelResource.ROOT).toFile(), "BlockActions/" + pos.getX() + "_" + pos.getY() + "_" + pos.getZ() + ".txt");

            if (blockFile.exists()) {
                try {
                    List<String> lines = Files.readAllLines(blockFile.toPath(), StandardCharsets.UTF_8);
                    for (String line : lines) {
                        if (line.contains("Action: R")) {
                            String command = line.substring(line.indexOf("Command: ") + 9).trim();
                            if (command.startsWith("/")) {
                                command = command.substring(1);
                                command = command.replace("<player>", player.getName().getString());
                            }

                            ParseResults<CommandSourceStack> parseResults = commands.getDispatcher().parse(new StringReader(command), player.createCommandSourceStack());
                            if (line.contains("Side: C")) {
                                commands.performCommand(parseResults, command);  // Übergabe des Befehls-Strings
                            } else if (line.contains("Side: S")) {
                                ParseResults<CommandSourceStack> serverParseResults = commands.getDispatcher().parse(new StringReader(command), world.getServer().createCommandSourceStack());
                                commands.performCommand(serverParseResults, command);  // Übergabe des Befehls-Strings
                            }
                        }
                    }
                } catch (Exception e) {
                    return;
                }
            }
        }
    }

    private static BlockPos lastBlockPos = null;

    @SubscribeEvent
    public static void onPlayerTick(TickEvent.PlayerTickEvent event) {
        Player player = event.player;

        if (!player.level().isClientSide && player instanceof ServerPlayer) {
            ServerPlayer serverPlayer = (ServerPlayer) player;

            BlockPos currentBlockPos = new BlockPos((int) Math.floor(player.getX()), (int) Math.floor(player.getY() - 1), (int) Math.floor(player.getZ()));


            if (serverPlayer.onGround()) {
                if (lastBlockPos == null || !currentBlockPos.equals(lastBlockPos)) {
                    BlockPos pos = currentBlockPos;
                    ServerLevel world = (ServerLevel) serverPlayer.level();
                    Commands commands = world.getServer().getCommands();

                    File blockFile = new File(world.getServer().getWorldPath(LevelResource.ROOT).toFile(), "BlockActions/" + pos.getX() + "_" + pos.getY() + "_" + pos.getZ() + ".txt");

                    if (blockFile.exists()) {
                        try {
                            List<String> lines = Files.readAllLines(blockFile.toPath(), StandardCharsets.UTF_8);
                            for (String line : lines) {
                                if (line.contains("Action: T")) {
                                    String command = line.substring(line.indexOf("Command: ") + 9).trim();
                                    if (command.startsWith("/")) {
                                        command = command.substring(1);
                                        command = command.replace("<player>", player.getName().getString());
                                    }

                                    ParseResults<CommandSourceStack> parseResults = commands.getDispatcher().parse(new StringReader(command), ((ServerPlayer) player).createCommandSourceStack());
                                    if (line.contains("Side: C")) {
                                        commands.performCommand(parseResults, command);  // Übergabe des Befehls-Strings
                                    } else if (line.contains("Side: S")) {
                                        ParseResults<CommandSourceStack> serverParseResults = commands.getDispatcher().parse(new StringReader(command), world.getServer().createCommandSourceStack());
                                        commands.performCommand(serverParseResults, command);  // Übergabe des Befehls-Strings
                                    }
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            return;
                        }
                    }

                    lastBlockPos = currentBlockPos;
                }
            } else {
                lastBlockPos = null;
            }
        }
    }



    @SubscribeEvent
    public static void onNeighborNotify(BlockEvent.NeighborNotifyEvent event) {
        if (!event.getLevel().isClientSide()) {
            ServerLevel world = (ServerLevel) event.getLevel();
            BlockPos pos = event.getPos();

            // Prüfen, ob das Signal von einem Redstone-Block kommt
            if (world.hasNeighborSignal(pos)) {
                // Führe Aktion aus, wenn Redstone-Signal anliegt
                File blockFile = new File(world.getServer().getWorldPath(LevelResource.ROOT).toFile(),
                        "BlockActions/" + pos.getX() + "_" + pos.getY() + "_" + pos.getZ() + ".txt");

                if (blockFile.exists()) {
                    try {
                        List<String> lines = Files.readAllLines(blockFile.toPath(), StandardCharsets.UTF_8);
                        for (String line : lines) {
                            if (line.contains("Action: S")) { // Redstone-Aktion
                                String command = line.substring(line.indexOf("Command: ") + 9).trim();
                                if (command.startsWith("/")) {
                                    command = command.substring(1);
                                }

                                // Befehl parsen und ausführen
                                CommandDispatcher<CommandSourceStack> dispatcher = world.getServer().getCommands().getDispatcher();
                                CommandSourceStack sourceStack = world.getServer().createCommandSourceStack();
                                ParseResults<CommandSourceStack> parsedCommand = dispatcher.parse(command, sourceStack);
                                dispatcher.execute(parsedCommand);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }




}
